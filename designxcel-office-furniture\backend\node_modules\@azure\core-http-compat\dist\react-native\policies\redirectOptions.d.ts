/**
 * Options for how redirect responses are handled.
 */
export interface RedirectOptions {
    /**
     * When true, redirect responses are followed.  Defaults to true.
     */
    handleRedirects?: boolean;
    /**
     * The maximum number of times the redirect URL will be tried before
     * failing.  Defaults to 20.
     */
    maxRetries?: number;
}
//# sourceMappingURL=redirectOptions.d.ts.map