{"version": 3, "file": "AbortSignalLike.js", "sourceRoot": "", "sources": ["../../src/AbortSignalLike.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Allows the request to be aborted upon firing of the \"abort\" event.\n * Compatible with the browser built-in AbortSignal and common polyfills.\n */\nexport interface AbortSignalLike {\n  /**\n   * Indicates if the signal has already been aborted.\n   */\n  readonly aborted: boolean;\n  /**\n   * Add new \"abort\" event listener, only support \"abort\" event.\n   */\n  addEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any,\n  ): void;\n  /**\n   * Remove \"abort\" event listener, only support \"abort\" event.\n   */\n  removeEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any,\n  ): void;\n}\n"]}