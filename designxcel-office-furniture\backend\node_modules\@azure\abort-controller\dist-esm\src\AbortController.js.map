{"version": 3, "file": "AbortController.js", "sourceRoot": "", "sources": ["../../src/AbortController.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,WAAW,EAAmB,WAAW,EAAE,MAAM,eAAe,CAAC;AAE1E;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,OAAO,UAAW,SAAQ,KAAK;IACnC,YAAY,OAAgB;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;IAC3B,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,OAAO,eAAe;IAW1B,6EAA6E;IAC7E,YAAY,aAAmB;QAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAEjC,IAAI,CAAC,aAAa,EAAE;YAClB,OAAO;SACR;QACD,qCAAqC;QACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YACjC,8CAA8C;YAC9C,aAAa,GAAG,SAAS,CAAC;SAC3B;QACD,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,uDAAuD;YACvD,0CAA0C;YAC1C,IAAI,YAAY,CAAC,OAAO,EAAE;gBACxB,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;iBAAM;gBACL,6DAA6D;gBAC7D,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;oBAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,CAAC,CAAC,CAAC;aACJ;SACF;IACH,CAAC;IAED;;;;;OAKG;IACH,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,OAAO,CAAC,EAAU;QAC9B,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;QACjC,MAAM,KAAK,GAAG,UAAU,CAAC,WAAW,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QAClD,uEAAuE;QACvE,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,UAAU,EAAE;YACrC,KAAK,CAAC,KAAK,EAAE,CAAC;SACf;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignal, AbortSignalLike, abortSignal } from \"./AbortSignal\";\n\n/**\n * This error is thrown when an asynchronous operation has been aborted.\n * Check for this error by testing the `name` that the name property of the\n * error matches `\"AbortError\"`.\n *\n * @example\n * ```ts\n * const controller = new AbortController();\n * controller.abort();\n * try {\n *   doAsyncWork(controller.signal)\n * } catch (e) {\n *   if (e.name === 'AbortError') {\n *     // handle abort error here.\n *   }\n * }\n * ```\n */\nexport class AbortError extends Error {\n  constructor(message?: string) {\n    super(message);\n    this.name = \"AbortError\";\n  }\n}\n\n/**\n * An AbortController provides an AbortSignal and the associated controls to signal\n * that an asynchronous operation should be aborted.\n *\n * @example\n * Abort an operation when another event fires\n * ```ts\n * const controller = new AbortController();\n * const signal = controller.signal;\n * doAsyncWork(signal);\n * button.addEventListener('click', () => controller.abort());\n * ```\n *\n * @example\n * Share aborter cross multiple operations in 30s\n * ```ts\n * // Upload the same data to 2 different data centers at the same time,\n * // abort another when any of them is finished\n * const controller = AbortController.withTimeout(30 * 1000);\n * doAsyncWork(controller.signal).then(controller.abort);\n * doAsyncWork(controller.signal).then(controller.abort);\n *```\n *\n * @example\n * Cascaded aborting\n * ```ts\n * // All operations can't take more than 30 seconds\n * const aborter = Aborter.timeout(30 * 1000);\n *\n * // Following 2 operations can't take more than 25 seconds\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * ```\n */\nexport class AbortController {\n  private _signal: AbortSignal;\n\n  /**\n   * @param parentSignals - The AbortSignals that will signal aborted on the AbortSignal associated with this controller.\n   */\n  constructor(parentSignals?: AbortSignalLike[]);\n  /**\n   * @param parentSignals - The AbortSignals that will signal aborted on the AbortSignal associated with this controller.\n   */\n  constructor(...parentSignals: AbortSignalLike[]);\n  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n  constructor(parentSignals?: any) {\n    this._signal = new AbortSignal();\n\n    if (!parentSignals) {\n      return;\n    }\n    // coerce parentSignals into an array\n    if (!Array.isArray(parentSignals)) {\n      // eslint-disable-next-line prefer-rest-params\n      parentSignals = arguments;\n    }\n    for (const parentSignal of parentSignals) {\n      // if the parent signal has already had abort() called,\n      // then call abort on this signal as well.\n      if (parentSignal.aborted) {\n        this.abort();\n      } else {\n        // when the parent signal aborts, this signal should as well.\n        parentSignal.addEventListener(\"abort\", () => {\n          this.abort();\n        });\n      }\n    }\n  }\n\n  /**\n   * The AbortSignal associated with this controller that will signal aborted\n   * when the abort method is called on this controller.\n   *\n   * @readonly\n   */\n  public get signal(): AbortSignal {\n    return this._signal;\n  }\n\n  /**\n   * Signal that any operations passed this controller's associated abort signal\n   * to cancel any remaining work and throw an `AbortError`.\n   */\n  abort(): void {\n    abortSignal(this._signal);\n  }\n\n  /**\n   * Creates a new AbortSignal instance that will abort after the provided ms.\n   * @param ms - Elapsed time in milliseconds to trigger an abort.\n   */\n  public static timeout(ms: number): AbortSignal {\n    const signal = new AbortSignal();\n    const timer = setTimeout(abortSignal, ms, signal);\n    // Prevent the active Timer from keeping the Node.js event loop active.\n    if (typeof timer.unref === \"function\") {\n      timer.unref();\n    }\n    return signal;\n  }\n}\n"]}