{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../../src/interfaces.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAclC;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,GAAG,CAAC;AAC/B;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,GAAG,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  HttpClient,\n  HttpMethods,\n  PipelineOptions,\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  TransferProgressEvent,\n} from \"@azure/core-rest-pipeline\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { OperationTracingOptions } from \"@azure/core-tracing\";\n\n/**\n * Default key used to access the XML attributes.\n */\nexport const XML_ATTRKEY = \"$\";\n/**\n * Default key used to access the XML value content.\n */\nexport const XML_CHARKEY = \"_\";\n/**\n * Options to govern behavior of xml parser and builder.\n */\nexport interface XmlOptions {\n  /**\n   * indicates the name of the root element in the resulting XML when building XML.\n   */\n  rootName?: string;\n  /**\n   * indicates whether the root element is to be included or not in the output when parsing XML.\n   */\n  includeRoot?: boolean;\n  /**\n   * key used to access the XML value content when parsing XML.\n   */\n  xmlCharKey?: string;\n}\n/**\n * Options to configure serialization/de-serialization behavior.\n */\nexport interface SerializerOptions {\n  /**\n   * Options to configure xml parser/builder behavior.\n   */\n  xml: XmlOptions;\n  /**\n   * Normally additional properties are included in the result object, even if there is no mapper for them.\n   * This flag disables this behavior when true. It is used when parsing headers to avoid polluting the result object.\n   */\n  ignoreUnknownProperties?: boolean;\n}\n\nexport type RequiredSerializerOptions = {\n  [K in keyof SerializerOptions]: Required<SerializerOptions[K]>;\n};\n\n/**\n * A type alias for future proofing.\n */\nexport type OperationRequest = PipelineRequest;\n\n/**\n * Metadata that is used to properly parse a response.\n */\nexport interface OperationRequestInfo {\n  /**\n   * Used to parse the response.\n   */\n  operationSpec?: OperationSpec;\n\n  /**\n   * Used to encode the request.\n   */\n  operationArguments?: OperationArguments;\n\n  /**\n   * A function that returns the proper OperationResponseMap for the given OperationSpec and\n   * PipelineResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: PipelineResponse,\n  ) => undefined | OperationResponseMap;\n\n  /**\n   * Whether or not the PipelineResponse should be deserialized. Defaults to true.\n   */\n  shouldDeserialize?: boolean | ((response: PipelineResponse) => boolean);\n}\n\n/**\n * The base options type for all operations.\n */\nexport interface OperationOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: OperationRequestOptions;\n  /**\n   * Options used when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n  /**\n   * Options to override serialization/de-serialization behavior.\n   */\n  serializerOptions?: SerializerOptions;\n\n  /**\n   * A function to be called each time a response is received from the server\n   * while performing the requested operation.\n   * May be called multiple times.\n   */\n  onResponse?: RawResponseCallback;\n}\n\n/**\n * Options used when creating and sending HTTP requests for this operation.\n */\nexport interface OperationRequestOptions {\n  /**\n   * User defined custom request headers that\n   * will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: PipelineResponse) => boolean);\n\n  /**\n   * Set to true if the request is sent over HTTP instead of HTTPS\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * A collection of properties that apply to a single invocation of an operation.\n */\nexport interface OperationArguments {\n  /**\n   * The parameters that were passed to the operation method.\n   */\n  [parameterName: string]: unknown;\n\n  /**\n   * The optional arguments that are provided to an operation.\n   */\n  options?: OperationOptions;\n}\n\n/**\n * The format that will be used to join an array of values together for a query parameter value.\n */\nexport type QueryCollectionFormat = \"CSV\" | \"SSV\" | \"TSV\" | \"Pipes\" | \"Multi\";\n\n/**\n * Encodes how to reach a particular property on an object.\n */\nexport type ParameterPath = string | string[] | { [propertyName: string]: ParameterPath };\n\n/**\n * A common interface that all Operation parameter's extend.\n */\nexport interface OperationParameter {\n  /**\n   * The path to this parameter's value in OperationArguments or the object that contains paths for\n   * each property's value in OperationArguments.\n   */\n  parameterPath: ParameterPath;\n\n  /**\n   * The mapper that defines how to validate and serialize this parameter's value.\n   */\n  mapper: Mapper;\n}\n\n/**\n * A parameter for an operation that will be substituted into the operation's request URL.\n */\nexport interface OperationURLParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the URL parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n}\n\n/**\n * A parameter for an operation that will be added as a query parameter to the operation's HTTP\n * request.\n */\nexport interface OperationQueryParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the query parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n\n  /**\n   * If this query parameter's value is a collection, what type of format should the value be\n   * converted to.\n   */\n  collectionFormat?: QueryCollectionFormat;\n}\n\n/**\n * An OperationResponse that can be returned from an operation request for a single status code.\n */\nexport interface OperationResponseMap {\n  /**\n   * The mapper that will be used to deserialize the response headers.\n   */\n  headersMapper?: Mapper;\n\n  /**\n   * The mapper that will be used to deserialize the response body.\n   */\n  bodyMapper?: Mapper;\n\n  /**\n   * Indicates if this is an error response\n   */\n  isError?: boolean;\n}\n\n/**\n * A specification that defines an operation.\n */\nexport interface OperationSpec {\n  /**\n   * The serializer to use in this operation.\n   */\n  readonly serializer: Serializer;\n\n  /**\n   * The HTTP method that should be used by requests for this operation.\n   */\n  readonly httpMethod: HttpMethods;\n\n  /**\n   * The URL that was provided in the service's specification. This will still have all of the URL\n   * template variables in it. If this is not provided when the OperationSpec is created, then it\n   * will be populated by a \"baseUri\" property on the ServiceClient.\n   */\n  readonly baseUrl?: string;\n\n  /**\n   * The fixed path for this operation's URL. This will still have all of the URL template variables\n   * in it.\n   */\n  readonly path?: string;\n\n  /**\n   * The content type of the request body. This value will be used as the \"Content-Type\" header if\n   * it is provided.\n   */\n  readonly contentType?: string;\n\n  /**\n   * The media type of the request body.\n   * This value can be used to aide in serialization if it is provided.\n   */\n  readonly mediaType?:\n    | \"json\"\n    | \"xml\"\n    | \"form\"\n    | \"binary\"\n    | \"multipart\"\n    | \"text\"\n    | \"unknown\"\n    | string;\n  /**\n   * The parameter that will be used to construct the HTTP request's body.\n   */\n  readonly requestBody?: OperationParameter;\n\n  /**\n   * Whether or not this operation uses XML request and response bodies.\n   */\n  readonly isXML?: boolean;\n\n  /**\n   * The parameters to the operation method that will be substituted into the constructed URL.\n   */\n  readonly urlParameters?: ReadonlyArray<OperationURLParameter>;\n\n  /**\n   * The parameters to the operation method that will be added to the constructed URL's query.\n   */\n  readonly queryParameters?: ReadonlyArray<OperationQueryParameter>;\n\n  /**\n   * The parameters to the operation method that will be converted to headers on the operation's\n   * HTTP request.\n   */\n  readonly headerParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The parameters to the operation method that will be used to create a formdata body for the\n   * operation's HTTP request.\n   */\n  readonly formDataParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The different types of responses that this operation can return based on what status code is\n   * returned.\n   */\n  readonly responses: { [responseCode: string]: OperationResponseMap };\n}\n\n/**\n * Wrapper object for http request and response. Deserialized object is stored in\n * the `parsedBody` property when the response body is received in JSON or XML.\n */\nexport interface FullOperationResponse extends PipelineResponse {\n  /**\n   * The parsed HTTP response headers.\n   */\n  parsedHeaders?: { [key: string]: unknown };\n\n  /**\n   * The response body as parsed JSON or XML.\n   */\n  parsedBody?: any;\n\n  /**\n   * The request that generated the response.\n   */\n  request: OperationRequest;\n}\n\n/**\n * A function to be called each time a response is received from the server\n * while performing the requested operation.\n * May be called multiple times.\n */\nexport type RawResponseCallback = (\n  rawResponse: FullOperationResponse,\n  flatResponse: unknown,\n  error?: unknown,\n) => void;\n\n/**\n * Used to map raw response objects to final shapes.\n * Helps packing and unpacking Dates and other encoded types that are not intrinsic to JSON.\n * Also allows pulling values from headers, as well as inserting default values and constants.\n */\nexport interface Serializer {\n  /**\n   * The provided model mapper.\n   */\n  readonly modelMappers: { [key: string]: any };\n  /**\n   * Whether the contents are XML or not.\n   */\n  readonly isXML: boolean;\n\n  /**\n   * Validates constraints, if any. This function will throw if the provided value does not respect those constraints.\n   * @param mapper - The definition of data models.\n   * @param value - The value.\n   * @param objectName - Name of the object. Used in the error messages.\n   * @deprecated Removing the constraints validation on client side.\n   */\n  validateConstraints(mapper: Mapper, value: any, objectName: string): void;\n\n  /**\n   * Serialize the given object based on its metadata defined in the mapper.\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object.\n   * @param object - A valid Javascript object to be serialized.\n   * @param objectName - Name of the serialized object.\n   * @param options - additional options to deserialization.\n   * @returns A valid serialized Javascript object.\n   */\n  serialize(mapper: Mapper, object: any, objectName?: string, options?: SerializerOptions): any;\n\n  /**\n   * Deserialize the given object based on its metadata defined in the mapper.\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object.\n   * @param responseBody - A valid Javascript entity to be deserialized.\n   * @param objectName - Name of the deserialized object.\n   * @param options - Controls behavior of XML parser and builder.\n   * @returns A valid deserialized Javascript object.\n   */\n  deserialize(\n    mapper: Mapper,\n    responseBody: any,\n    objectName: string,\n    options?: SerializerOptions,\n  ): any;\n}\n\n/**\n * Description of various value constraints such as integer ranges and string regex.\n */\nexport interface MapperConstraints {\n  /**\n   * The value should be less than or equal to the `InclusiveMaximum` value.\n   */\n  InclusiveMaximum?: number;\n  /**\n   * The value should be less than the `ExclusiveMaximum` value.\n   */\n  ExclusiveMaximum?: number;\n  /**\n   * The value should be greater than or equal to the `InclusiveMinimum` value.\n   */\n  InclusiveMinimum?: number;\n  /**\n   * The value should be greater than the `InclusiveMinimum` value.\n   */\n  ExclusiveMinimum?: number;\n  /**\n   * The length should be smaller than the `MaxLength`.\n   */\n  MaxLength?: number;\n  /**\n   * The length should be bigger than the `MinLength`.\n   */\n  MinLength?: number;\n  /**\n   * The value must match the pattern.\n   */\n  Pattern?: RegExp;\n  /**\n   * The value must contain fewer items than the MaxItems value.\n   */\n  MaxItems?: number;\n  /**\n   * The value must contain more items than the `MinItems` value.\n   */\n  MinItems?: number;\n  /**\n   * The value must contain only unique items.\n   */\n  UniqueItems?: true;\n  /**\n   * The value should be exactly divisible by the `MultipleOf` value.\n   */\n  MultipleOf?: number;\n}\n\n/**\n * Type of the mapper. Includes known mappers.\n */\nexport type MapperType =\n  | SimpleMapperType\n  | CompositeMapperType\n  | SequenceMapperType\n  | DictionaryMapperType\n  | EnumMapperType;\n\n/**\n * The type of a simple mapper.\n */\nexport interface SimpleMapperType {\n  /**\n   * Name of the type of the property.\n   */\n  name:\n    | \"Base64Url\"\n    | \"Boolean\"\n    | \"ByteArray\"\n    | \"Date\"\n    | \"DateTime\"\n    | \"DateTimeRfc1123\"\n    | \"Object\"\n    | \"Stream\"\n    | \"String\"\n    | \"TimeSpan\"\n    | \"UnixTime\"\n    | \"Uuid\"\n    | \"Number\"\n    | \"any\";\n}\n\n/**\n * Helps build a mapper that describes how to map a set of properties of an object based on other mappers.\n *\n * Only one of the following properties should be present: `className`, `modelProperties` and `additionalProperties`.\n */\nexport interface CompositeMapperType {\n  /**\n   * Name of the composite mapper type.\n   */\n  name: \"Composite\";\n\n  /**\n   * Use `className` to reference another type definition.\n   */\n  className?: string;\n\n  /**\n   * Use `modelProperties` when the reference to the other type has been resolved.\n   */\n  modelProperties?: { [propertyName: string]: Mapper };\n\n  /**\n   * Used when a model has `additionalProperties: true`. Allows the generic processing of unnamed model properties on the response object.\n   */\n  additionalProperties?: Mapper;\n\n  /**\n   * The name of the top-most parent scheme, the one that has no parents.\n   */\n  uberParent?: string;\n\n  /**\n   * A polymorphic discriminator.\n   */\n  polymorphicDiscriminator?: PolymorphicDiscriminator;\n}\n\n/**\n * Helps build a mapper that describes how to parse a sequence of mapped values.\n */\nexport interface SequenceMapperType {\n  /**\n   * Name of the sequence type mapper.\n   */\n  name: \"Sequence\";\n  /**\n   * The mapper to use to map each one of the properties of the sequence.\n   */\n  element: Mapper;\n}\n\n/**\n * Helps build a mapper that describes how to parse a dictionary of mapped values.\n */\nexport interface DictionaryMapperType {\n  /**\n   * Name of the sequence type mapper.\n   */\n  name: \"Dictionary\";\n  /**\n   * The mapper to use to map the value of each property in the dictionary.\n   */\n  value: Mapper;\n}\n\n/**\n * Helps build a mapper that describes how to parse an enum value.\n */\nexport interface EnumMapperType {\n  /**\n   * Name of the enum type mapper.\n   */\n  name: \"Enum\";\n  /**\n   * Values allowed by this mapper.\n   */\n  allowedValues: any[];\n}\n\n/**\n * The base definition of a mapper. Can be used for XML and plain JavaScript objects.\n */\nexport interface BaseMapper {\n  /**\n   * Name for the xml element\n   */\n  xmlName?: string;\n  /**\n   * Xml element namespace\n   */\n  xmlNamespace?: string;\n  /**\n   * Xml element namespace prefix\n   */\n  xmlNamespacePrefix?: string;\n  /**\n   * Determines if the current property should be serialized as an attribute of the parent xml element\n   */\n  xmlIsAttribute?: boolean;\n  /**\n   * Determines if the current property should be serialized as the inner content of the xml element\n   */\n  xmlIsMsText?: boolean;\n  /**\n   * Name for the xml elements when serializing an array\n   */\n  xmlElementName?: string;\n  /**\n   * Whether or not the current property should have a wrapping XML element\n   */\n  xmlIsWrapped?: boolean;\n  /**\n   * Whether or not the current property is readonly\n   */\n  readOnly?: boolean;\n  /**\n   * Whether or not the current property is a constant\n   */\n  isConstant?: boolean;\n  /**\n   * Whether or not the current property is required\n   */\n  required?: boolean;\n  /**\n   * Whether or not the current property allows mull as a value\n   */\n  nullable?: boolean;\n  /**\n   * The name to use when serializing\n   */\n  serializedName?: string;\n  /**\n   * Type of the mapper\n   */\n  type: MapperType;\n  /**\n   * Default value when one is not explicitly provided\n   */\n  defaultValue?: any;\n  /**\n   * Constraints to test the current value against\n   */\n  constraints?: MapperConstraints;\n}\n\n/**\n * Mappers are definitions of the data models used in the library.\n * These data models are part of the Operation or Client definitions in the responses or parameters.\n */\nexport type Mapper = BaseMapper | CompositeMapper | SequenceMapper | DictionaryMapper | EnumMapper;\n\n/**\n * Used to disambiguate discriminated type unions.\n * For example, if response can have many shapes but also includes a 'kind' field (or similar),\n * that field can be used to determine how to deserialize the response to the correct type.\n */\nexport interface PolymorphicDiscriminator {\n  /**\n   * Name of the discriminant property in the original JSON payload, e.g. `@odata.kind`.\n   */\n  serializedName: string;\n  /**\n   * Name to use on the resulting object instead of the original property name.\n   * Useful since the JSON property could be difficult to work with.\n   * For example: For a field received as `@odata.kind`, the final object could instead include a property simply named `kind`.\n   */\n  clientName: string;\n  /**\n   * It may contain any other property.\n   */\n  [key: string]: string;\n}\n\n/**\n * A mapper composed of other mappers.\n */\nexport interface CompositeMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `CompositeMapper`.\n   */\n  type: CompositeMapperType;\n}\n\n/**\n * A mapper describing arrays.\n */\nexport interface SequenceMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `SequenceMapper`.\n   */\n  type: SequenceMapperType;\n}\n\n/**\n * A mapper describing plain JavaScript objects used as key/value pairs.\n */\nexport interface DictionaryMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `DictionaryMapper`.\n   */\n  type: DictionaryMapperType;\n  /**\n   * Optionally, a prefix to add to the header collection.\n   */\n  headerCollectionPrefix?: string;\n}\n\n/**\n * A mapper describing an enum value.\n */\nexport interface EnumMapper extends BaseMapper {\n  /**\n   * The type descriptor of the `EnumMapper`.\n   */\n  type: EnumMapperType;\n}\n\nexport interface UrlParameterValue {\n  value: string;\n  skipUrlEncoding: boolean;\n}\n\n/**\n * Configuration for creating a new Tracing Span\n */\nexport interface SpanConfig {\n  /**\n   * Package name prefix\n   */\n  packagePrefix: string;\n  /**\n   * Service namespace\n   */\n  namespace: string;\n}\n\n/**\n * Used to configure additional policies added to the pipeline at construction.\n */\nexport interface AdditionalPolicyConfig {\n  /**\n   * A policy to be added.\n   */\n  policy: PipelinePolicy;\n  /**\n   * Determines if this policy be applied before or after retry logic.\n   * Only use `perRetry` if you need to modify the request again\n   * each time the operation is retried due to retryable service\n   * issues.\n   */\n  position: \"perCall\" | \"perRetry\";\n}\n\n/**\n * The common set of options that high level clients are expected to expose.\n */\nexport interface CommonClientOptions extends PipelineOptions {\n  /**\n   * The HttpClient that will be used to send HTTP requests.\n   */\n  httpClient?: HttpClient;\n  /**\n   * Set to true if the request is sent over HTTP instead of HTTPS\n   */\n  allowInsecureConnection?: boolean;\n  /**\n   * Additional policies to include in the HTTP pipeline.\n   */\n  additionalPolicies?: AdditionalPolicyConfig[];\n}\n"]}