import type { OperationArguments, OperationSpec } from "./interfaces.js";
export declare function getRequestUrl(baseUri: string, operationSpec: OperationSpec, operationArguments: OperationArguments, fallbackObject: {
    [parameterName: string]: any;
}): string;
/** @internal */
export declare function appendQueryParams(url: string, queryParams: Map<string, string | string[]>, sequenceParams: Set<string>, noOverwrite?: boolean): string;
//# sourceMappingURL=urlHelpers.d.ts.map