{"name": "@azure/core-auth", "version": "1.9.0", "description": "Provides low-level interfaces and helper methods for authentication in Azure SDK", "sdk-type": "client", "type": "module", "exports": {"./package.json": "./package.json", ".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "react-native": {"types": "./dist/react-native/index.d.ts", "default": "./dist/react-native/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "browser": "./dist/browser/index.js", "react-native": "./dist/react-native/index.js", "files": ["dist/", "README.md", "LICENSE"], "repository": "github:Azure/azure-sdk-for-js", "keywords": ["azure", "authentication", "cloud"], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "engines": {"node": ">=18.0.0"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/core-auth/README.md", "sideEffects": false, "scripts": {"build": "npm run clean && dev-tool run build-package && dev-tool run extract-api", "build:samples": "echo Obsolete", "build:test": "echo skipped. actual commands inlined in browser test scripts", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,cjs,mjs,json}\"", "clean": "rimraf --glob dist dist-* temp types *.tgz *.log", "execute:samples": "echo skipped", "extract-api": "dev-tool run build-package && dev-tool run extract-api", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,cjs,mjs,json}\"", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "lint": "eslint package.json api-extractor.json src test", "lint:fix": "eslint package.json api-extractor.json src test --fix --fix-type [problem,suggestion]", "pack": "npm pack 2>&1", "test": "npm run clean && dev-tool run build-package && npm run unit-test:node && dev-tool run bundle && npm run unit-test:browser && npm run integration-test", "test:browser": "npm run clean && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && dev-tool run build-package && npm run unit-test:node && npm run integration-test:node", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "npm run clean && dev-tool run build-package && dev-tool run build-test && dev-tool run test:vitest --no-test-proxy --browser", "unit-test:node": "dev-tool run test:vitest --no-test-proxy", "update-snippets": "dev-tool run update-snippets"}, "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.11.0", "tslib": "^2.6.2"}, "devDependencies": {"@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@microsoft/api-extractor": "^7.40.3", "@types/node": "^18.0.0", "@vitest/browser": "^2.0.5", "@vitest/coverage-istanbul": "^2.0.5", "eslint": "^9.9.0", "playwright": "^1.41.2", "rimraf": "^5.0.5", "typescript": "~5.6.2", "vitest": "^2.0.5"}, "//metadata": {"migrationDate": "2023-03-08T18:36:03.000Z"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["browser", "react-native"], "selfLink": false}, "module": "./dist/esm/index.js"}