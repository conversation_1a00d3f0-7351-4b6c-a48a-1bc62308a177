{"version": 3, "file": "response.js", "sourceRoot": "", "sources": ["../../src/response.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAE9D,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AAepF,MAAM,gBAAgB,GAAG,MAAM,CAAC,gCAAgC,CAAC,CAAC;AAGlE;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAC9B,QAA+B,EAC/B,OAAmC;IAEnC,IAAI,OAAO,GAAG,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAClD,IAAI,OAAO,GAAG,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAClD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,CAAC;QACzB,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE;YACzB,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ;gBACxB,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBACvB,OAAO,OAAO,CAAC;gBACjB,CAAC;qBAAM,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC9B,OAAO,OAAO,CAAC;gBACjB,CAAC;qBAAM,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;oBACrC,OAAO,QAAQ,CAAC;gBAClB,CAAC;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC7C,CAAC;YACD,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ;gBAC/B,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBACvB,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;qBAAM,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC9B,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACpD,CAAC;SACF,CAA8B,CAAC;IAClC,CAAC;SAAM,CAAC;QACN,uCACK,QAAQ,KACX,OAAO;YACP,OAAO,IACP;IACJ,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB,CAAC,cAA8B;IAC/D,MAAM,sBAAsB,GAAG,cAAwC,CAAC;IACxE,MAAM,QAAQ,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;IAC1D,MAAM,OAAO,GAAG,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzF,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;QAC3B,OAAO,QAAQ,CAAC;IAClB,CAAC;SAAM,CAAC;QACN,uCACK,cAAc,KACjB,OAAO,EACP,OAAO,EAAE,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC,IAClD;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { FullOperationResponse } from \"@azure/core-client\";\nimport type { PipelineResponse } from \"@azure/core-rest-pipeline\";\nimport { createHttpHeaders } from \"@azure/core-rest-pipeline\";\nimport type { HttpHeadersLike, WebResourceLike } from \"./util.js\";\nimport { toHttpHeadersLike, toPipelineRequest, toWebResourceLike } from \"./util.js\";\n/**\n * Http Response that is compatible with the core-v1(core-http).\n */\nexport interface CompatResponse extends Omit<FullOperationResponse, \"request\" | \"headers\"> {\n  /**\n   * A description of a HTTP request to be made to a remote server.\n   */\n  request: WebResourceLike;\n  /**\n   * A collection of HTTP header key/value pairs.\n   */\n  headers: HttpHeadersLike;\n}\n\nconst originalResponse = Symbol(\"Original FullOperationResponse\");\ntype ExtendedCompatResponse = CompatResponse & { [originalResponse]?: FullOperationResponse };\n\n/**\n * A helper to convert response objects from the new pipeline back to the old one.\n * @param response - A response object from core-client.\n * @returns A response compatible with `HttpOperationResponse` from core-http.\n */\nexport function toCompatResponse(\n  response: FullOperationResponse,\n  options?: { createProxy?: boolean },\n): CompatResponse {\n  let request = toWebResourceLike(response.request);\n  let headers = toHttpHeadersLike(response.headers);\n  if (options?.createProxy) {\n    return new Proxy(response, {\n      get(target, prop, receiver) {\n        if (prop === \"headers\") {\n          return headers;\n        } else if (prop === \"request\") {\n          return request;\n        } else if (prop === originalResponse) {\n          return response;\n        }\n        return Reflect.get(target, prop, receiver);\n      },\n      set(target, prop, value, receiver) {\n        if (prop === \"headers\") {\n          headers = value;\n        } else if (prop === \"request\") {\n          request = value;\n        }\n        return Reflect.set(target, prop, value, receiver);\n      },\n    }) as unknown as CompatResponse;\n  } else {\n    return {\n      ...response,\n      request,\n      headers,\n    };\n  }\n}\n\n/**\n * A helper to convert back to a PipelineResponse\n * @param compatResponse - A response compatible with `HttpOperationResponse` from core-http.\n */\nexport function toPipelineResponse(compatResponse: CompatResponse): PipelineResponse {\n  const extendedCompatResponse = compatResponse as ExtendedCompatResponse;\n  const response = extendedCompatResponse[originalResponse];\n  const headers = createHttpHeaders(compatResponse.headers.toJson({ preserveCase: true }));\n  if (response) {\n    response.headers = headers;\n    return response;\n  } else {\n    return {\n      ...compatResponse,\n      headers,\n      request: toPipelineRequest(compatResponse.request),\n    };\n  }\n}\n"]}